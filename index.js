const logger = require('./config/logger');
const cron = require('node-cron');
const config = require("./config/config");
const connectRabbitmq = require("./config/rabbitmq");
const { inboundAgentHandler } = require('./agents/inbound.agent');
const { outboundAgentHandler } = require('./agents/outbound.agent');

let connection, channel = global.channel;

// Parse command line arguments
const args = process.argv.slice(2);
const queueIndex = args.indexOf('--queue');
const queueName = queueIndex !== -1 && args[queueIndex + 1] ? args[queueIndex + 1] : null;

(async () => {
    // Await RabbitMQ connection first
    if (config.messageQueuing.status && !channel) {
        const rabbit = await connectRabbitmq();
        if (!rabbit || !rabbit.connection || !rabbit.channel) {
        throw new Error("RabbitMQ connect didn't return a valid object");
        }
        connection, channel = rabbit.connection, rabbit.channel;
    }

    global.APP_CONTEXT = { function: null, application: "agent" };

    const { getCachedAgentSources } = require('./utils/caching');

    try {
        if (queueName) {
            // Single agent mode: get agent by queue name
            logger.info(`Running in single agent mode for queue: ${queueName}`);

            const rawAgents = await getCachedAgentSources();
            const targetAgent = rawAgents.find(agent => agent.queue === queueName && agent.status === true);

            if (!targetAgent) {
                logger.error(`No active agent found for queue: ${queueName}`);
                process.exit(1);
            }

            logger.info(`Found agent: ${targetAgent.name} (Type: ${targetAgent.type})`);

            // Process based on agent type
            if (targetAgent.type === 'Inbound') {
                await inboundAgentHandler(null, targetAgent);
            } else if (targetAgent.type === 'Outbound') {
                await outboundAgentHandler(targetAgent);
            } else {
                logger.error(`Unknown agent type: ${targetAgent.type}`);
                process.exit(1);
            }

            logger.info(`Agent processing completed for queue: ${queueName}`);
        } else {
            // Original multi-agent mode with cron scheduling
            logger.info('Running in multi-agent mode with cron scheduling');

            // Fetch active cron job configurations from the database
            const { CronConfig } = require('./models'); // Assuming models are initialized properly
            const cronJobs = await CronConfig.findAll({ where: { is_active: true } });
            // Get all agent sources from cache
            const rawAgents = await getCachedAgentSources();
            // Group agents by type using a mapping object for scalability
            const agentsByType = { inbound: [], outbound: [] };
            const typeMap = {
                inbound: agentsByType.inbound,
                outbound: agentsByType.outbound
            };
            (rawAgents || []).forEach(agent => {
                if (agent.type && typeMap[agent.type]) {
                    typeMap[agent.type].push(agent);
                } else {
                    // Optionally handle unknown types
                    logger.warn(`Unknown agent type: ${agent.type}`);
                }
            });
            // Inbound: schedule cron jobs and process as before
            async function inboundJobWrapper(job = null) {
                await inboundAgentHandler(job);
            }
            if (agentsByType.inbound.length > 0) {
                await inboundJobWrapper();
                cronJobs.forEach((job) => {
                    cron.schedule(job.schedule, async () => {
                        logger.info(`Executing job: ${job.name}`);
                        await inboundJobWrapper(job);
                    });
                });
                logger.info('Inbound cron jobs scheduled successfully.');
            }
            // Outbound: process each outbound agent (no cron)
            if (agentsByType.outbound.length > 0) {
                for (const agent of agentsByType.outbound) {
                    await outboundAgentHandler(agent);
                }
            }
        }
    } catch (err) {
        logger.error('Fatal error:', err);
        process.exit(1);
    }
})();
