const logger = require('./config/logger');
const cron = require('node-cron');
const config = require("./config/config");
const connectRabbitmq = require("./config/rabbitmq");
const { inboundAgentHandler } = require('./agents/inbound.agent');
const { outboundAgentHandler } = require('./agents/outbound.agent');

let connection, channel = global.channel;

(async () => {
    // Await RabbitMQ connection first
    if (config.messageQueuing.status && !channel) {
        const rabbit = await connectRabbitmq();
        if (!rabbit || !rabbit.connection || !rabbit.channel) {
        throw new Error("RabbitMQ connect didn't return a valid object");
        }
        connection, channel = rabbit.connection, rabbit.channel;
    }

    global.APP_CONTEXT = { function: null, application: "agent" };

    const { getCachedAgentSources } = require('./utils/caching');

    try {
        // Fetch active cron job configurations from the database
        const { CronConfig } = require('./models'); // Assuming models are initialized properly
        const cronJobs = await CronConfig.findAll({ where: { is_active: true } });
        // Get all agent sources from cache
        const rawAgents = await getCachedAgentSources();
        // Group agents by type using a mapping object for scalability
        const agentsByType = { inbound: [], outbound: [] };
        const typeMap = {
            inbound: agentsByType.inbound,
            outbound: agentsByType.outbound
        };
        (rawAgents || []).forEach(agent => {
            if (agent.type && typeMap[agent.type]) {
                typeMap[agent.type].push(agent);
            } else {
                // Optionally handle unknown types
                logger.warn(`Unknown agent type: ${agent.type}`);
            }
        });
        // Inbound: schedule cron jobs and process as before
        async function inboundJobWrapper(job = null) {
            await inboundAgentHandler(job);
        }
        if (agentsByType.inbound.length > 0) {
            await inboundJobWrapper();
            cronJobs.forEach((job) => {
                cron.schedule(job.schedule, async () => {
                    logger.info(`Executing job: ${job.name}`);
                    await inboundJobWrapper(job);
                });
            });
            logger.info('Inbound cron jobs scheduled successfully.');
        }
        // Outbound: process each outbound agent (no cron)
        if (agentsByType.outbound.length > 0) {
            for (const agent of agentsByType.outbound) {
                await outboundAgentHandler(agent);
            }
        }
    } catch (err) {
        logger.error('Fatal error:', err);
        process.exit(1);
    }
})();
