// Handles outbound agent processing (no cron, different logic)
const fs = require('fs');
const path = require('path');
const csv = require('fast-csv');
const logger = require('../config/logger');
const PerformanceMonitor = require('../utils/performance');

/**
 * Loads mapping configuration from the mappings directory
 * @param {string} mappingName - Name of the mapping file (without .mapping.json extension)
 * @returns {Object} Mapping configuration object
 */
function loadMapping(mappingName) {
    try {
        const mappingPath = path.join(__dirname, '..', 'mappings', `${mappingName}.mapping.json`);
        if (!fs.existsSync(mappingPath)) {
            throw new Error(`Mapping file not found: ${mappingPath}`);
        }
        const mappingContent = fs.readFileSync(mappingPath, 'utf8');
        return JSON.parse(mappingContent);
    } catch (error) {
        logger.error(`Error loading mapping ${mappingName}:`, error.message);
        throw error;
    }
}

/**
 * Generates CSV data from Identity model using reversed mapping
 * @param {Object} agent - Agent configuration
 * @param {Object} performanceMonitor - Performance monitoring instance
 * @returns {Promise<string>} Path to generated CSV file
 */
async function generateCSVFromIdentity(agent, performanceMonitor) {
    const { Identity } = require('../models');

    try {
        // Load mapping configuration
        const mappingConfig = loadMapping(agent.mapping);
        logger.info(`Loaded mapping configuration for: ${agent.mapping}`);

        // Get all Identity records
        performanceMonitor?.startStep('Fetch Identity Data', { agentName: agent.name });
        const identityRecords = await Identity.findAll({
            raw: true,
            limit: agent.batch_size || 1000 // Limit records based on batch size
        });
        performanceMonitor?.endStep('Fetch Identity Data', { recordCount: identityRecords.length });

        if (identityRecords.length === 0) {
            logger.warn('No Identity records found for CSV generation');
            return null;
        }

        // Prepare CSV data using reversed mapping
        performanceMonitor?.startStep('Transform Data', { recordCount: identityRecords.length });
        const csvData = [];
        const headers = Object.values(mappingConfig.mappings); // Get CSV column headers

        // Add headers as first row
        csvData.push(headers);

        // Transform each Identity record
        identityRecords.forEach(record => {
            const csvRow = [];

            // For each CSV column, get the corresponding Identity field value
            Object.entries(mappingConfig.mappings).forEach(([identityField, csvColumn]) => {
                // Extract field name from "Identity.field_name" format
                const fieldName = identityField.replace('Identity.', '');
                const value = record[fieldName] || ''; // Get value or empty string
                csvRow.push(value);
            });

            csvData.push(csvRow);
        });
        performanceMonitor?.endStep('Transform Data', { transformedRows: csvData.length - 1 });

        // Ensure output directory exists
        const outputDir = agent.settingsObj?.directory_path || './downloads/outbound';
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
            logger.info(`Created output directory: ${outputDir}`);
        }

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `hr_data_export_${timestamp}.csv`;
        const filePath = path.join(outputDir, filename);

        // Write CSV file
        performanceMonitor?.startStep('Write CSV File', { filePath, rowCount: csvData.length });
        await new Promise((resolve, reject) => {
            const writeStream = fs.createWriteStream(filePath);
            csv.write(csvData, { headers: false })
                .pipe(writeStream)
                .on('finish', resolve)
                .on('error', reject);
        });
        performanceMonitor?.endStep('Write CSV File', { fileSize: fs.statSync(filePath).size });

        logger.info(`CSV file generated successfully: ${filePath}`);
        logger.info(`Generated ${csvData.length - 1} data rows with ${headers.length} columns`);

        return filePath;

    } catch (error) {
        logger.error('Error generating CSV from Identity data:', error.message);
        throw error;
    }
}

async function outboundAgentHandler(agent) {
    const performanceMonitor = new PerformanceMonitor('Outbound CSV Generation');

    try {
        logger.info(`Processing outbound agent: ${agent.name} (Queue: ${agent.queue})`);

        performanceMonitor.startStep('Overall Outbound Processing', {
            agentName: agent.name,
            agentType: agent.type,
            mapping: agent.mapping,
            batchSize: agent.batch_size
        });

        // Generate CSV file from Identity data
        const csvFilePath = await generateCSVFromIdentity(agent, performanceMonitor);

        if (csvFilePath) {
            const finalMetrics = performanceMonitor.complete({
                status: 'success',
                generatedFile: csvFilePath,
                agentName: agent.name
            });

            logger.info('Outbound agent processing completed successfully.', {
                performanceMetrics: finalMetrics.summary,
                generatedFile: csvFilePath
            });
        } else {
            performanceMonitor.complete({
                status: 'completed_no_data',
                reason: 'No Identity records found'
            });
            logger.info('Outbound agent processing completed - no data to export');
        }

    } catch (error) {
        logger.error(`Error in outbound agent processing for ${agent.name}:`, error.message);
        performanceMonitor.complete({
            status: 'error',
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

module.exports = { outboundAgentHandler };
