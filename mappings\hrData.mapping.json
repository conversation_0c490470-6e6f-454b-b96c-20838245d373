{"mappingType": "columnName", "mappings": {"Identity.email": "email", "Identity.first_name": "firstName", "Identity.last_name": "lastName", "Identity.middle_name": "middleName", "Identity.eid": "employeeId", "Identity.identity_type": "identityType", "Identity.national_id": "nationalId", "Identity.suffix": "suffix", "Identity.mobile": "mobile", "Identity.start_date": "startDate", "Identity.end_date": "endDate", "Identity.status": "status", "Identity.company": "company", "Identity.organization": "organization", "Identity.company_code": "companyCode", "Identity.job_title": "jobTitle", "Identity.job_code": "jobCode"}, "columnIndex": {"Identity.email": "0", "Identity.first_name": "1", "Identity.last_name": "2", "Identity.middle_name": "3", "Identity.eid": "4", "Identity.identity_type": "5", "Identity.national_id": "6", "Identity.suffix": "7", "Identity.mobile": "8", "Identity.start_date": "9", "Identity.end_date": "10", "Identity.status": "11", "Identity.company": "12", "Identity.organization": "13", "Identity.company_code": "14", "Identity.job_title": "15", "Identity.job_code": "16"}, "required": ["Identity.email", "Identity.first_name", "Identity.last_name"], "validation": {"Identity.email": {"type": "email", "required": true}, "Identity.first_name": {"type": "string", "required": true}, "Identity.last_name": {"type": "string", "required": true}, "Identity.identity_type": {"type": "number"}, "Identity.status": {"type": "number"}}}